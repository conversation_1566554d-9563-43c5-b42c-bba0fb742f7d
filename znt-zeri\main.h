/**
 * @file main.h
 * @brief Core header file for Zeri++ League of Legends bot
 *
 * This file contains the main declarations, constants, and utility functions
 * for the Zeri champion automation script. It provides damage calculations,
 * target validation, and core game state management.
 *
 * <AUTHOR>
 * @version 2.0
 */

#pragma once
#include "config.h"
#include "sdk.h"

namespace zeri {
    //=============================================================================
    // MATHEMATICAL CONSTANTS
    //=============================================================================

    /** @brief Mathematical constant PI for angle calculations */
    constexpr f32 PI = 3.1415926535f;

    //=============================================================================
    // GLOBAL GAME STATE VARIABLES
    //=============================================================================

    /** @brief Current game time in seconds */
    inline static f32 time = 0.0f;

    /** @brief Next allowed spell cast time (for rate limiting) */
    inline static f32 next_cast = 0.0f;

    /** @brief Reference to the player's champion object */
    inline static AIHeroClient* player;

    /** @brief Reference to Zeri's Q spell instance */
    inline static SpellInstance* zeri_q;

    /** @brief Reference to Zeri's W spell instance */
    inline static SpellInstance* zeri_w;

    /** @brief Reference to Zeri's E spell instance */
    inline static SpellInstance* zeri_e;

    /** @brief Reference to Zeri's R spell instance */
    inline static SpellInstance* zeri_r;

    //=============================================================================
    // SPELL CONSTANTS (Based on League of Legends patch data)
    //=============================================================================

    /** @brief Rate limiting for spell casts to prevent spam (seconds) */
    constexpr f32 CAST_RATE = 0.25f;

    /** @brief Zeri E (Spark Surge) maximum dash range */
    constexpr f32 E_RANGE = 300.0f;

    /** @brief Zeri R (Lightning Crash) cast range */
    constexpr f32 R_RANGE = 825.0f;

    /** @brief Zeri R (Lightning Crash) projectile speed */
    constexpr f32 R_SPEED = 2000.0f;

    /** @brief Zeri Q base range (scales with bonus attack range) */
    constexpr f32 Q_BASE_RANGE = 750.0f;

    //=============================================================================
    // UI THEME COLORS
    //=============================================================================

    /** @brief Theme color red component (light purple theme) */
    constexpr u8 THEME_RED = 147;

    /** @brief Theme color green component (light purple theme) */
    constexpr u8 THEME_GREEN = 112;

    /** @brief Theme color blue component (light purple theme) */
    constexpr u8 THEME_BLUE = 219;

    /** @brief Theme color alpha component (semi-transparent) */
    constexpr u8 THEME_ALPHA = 200;

    //=============================================================================
    // COMBAT CALCULATION CONSTANTS
    //=============================================================================

    /** @brief Minimum auto attacks to consider in damage calculations */
    constexpr i32 MIN_AUTO_ATTACKS = 3;

    /** @brief Maximum auto attacks to consider in damage calculations */
    constexpr i32 MAX_AUTO_ATTACKS = 12;

    /** @brief Maximum champion level for scaling calculations */
    constexpr i32 MAX_LEVEL = 18;

    //=============================================================================
    // SPELL READINESS FUNCTIONS
    //=============================================================================

    /**
     * @brief Checks if Zeri's Q spell is ready to cast
     *
     * Takes into account cooldown remaining and network latency to determine
     * if the Q spell can be cast effectively.
     *
     * @return true if Q is ready to cast, false otherwise
     */
    inline static bool q_ready() {
        auto spell = player->get_spell(SpellSlot::Q);
        if (!spell) {
            return false;
        }

        return spell->cool_down_remaining() <= (get_latency() / 1000) + 0.5f;
    }

    //=============================================================================
    // COMBAT CALCULATION FUNCTIONS
    //=============================================================================

    /**
     * @brief Calculates dynamic auto attack count based on player level
     *
     * Scales linearly from MIN_AUTO_ATTACKS at level 1 to MAX_AUTO_ATTACKS
     * at maximum level. Used for damage calculations to estimate realistic
     * combat scenarios.
     *
     * @return Number of auto attacks to consider in damage calculations
     */
    inline static i32 get_dynamic_auto_attack_count() {
        i32 current_level = player->level();
        // Clamp level between 1 and MAX_LEVEL for safety
        current_level = max(1, min(current_level, MAX_LEVEL));

        // Calculate scaling factor (0.0 to 1.0)
        f32 level_factor = static_cast<f32>(current_level - 1) / static_cast<f32>(MAX_LEVEL - 1);

        // Linear interpolation between MIN and MAX auto attacks
        f32 attack_count = MIN_AUTO_ATTACKS + (MAX_AUTO_ATTACKS - MIN_AUTO_ATTACKS) * level_factor;

        // Round to nearest integer
        return static_cast<i32>(round(attack_count));
    }

    /**
     * @brief Calculates Zeri's passive (Burst Fire) damage
     *
     * Zeri's passive has two modes:
     * - Normal: 10-25 (+3% AP) magic damage with execute threshold 60-150 (+18% AP)
     * - Charged (100 energy): 75-160 (+110% AP) + 1%-11% max health magic damage
     *
     * @param target The target to calculate damage against
     * @param charged Whether Zeri has 100 energy (charged state)
     * @return Actual damage after magic resistance calculation
     */
    inline static f32 calc_passive_damage(AIBaseClient* target, bool charged = false) {
        if (!target) {
            return 0.0f;
        }

        // Get player level and clamp to valid range (1-18)
        AIHeroClient* hero_player   = static_cast<AIHeroClient*>(player);
        u32           current_level = hero_player->level();
        if (current_level == 0)
            current_level = 1;  // Treat level 0 as level 1 for safety
        u32 clamped_level = max(1u, min(current_level, 18u));

        f32 ap_value = player->magical_damage();  // Assuming this field holds Ability Power

        if (charged) {
            // Full charge damage calculation: 75-160 (based on level) + 110% AP + 1%-11% (based on level) of target's max health
            f32 base_charged_damage = 75.0f + (85.0f / 17.0f) * (f32) (clamped_level - 1);
            base_charged_damage     = max(75.0f, min(base_charged_damage, 160.0f));

            f32 ap_ratio_charged = 1.10f * ap_value;

            // Calculate percent max health damage (1%-11% based on level)
            f32 percent_max_health_ratio  = 0.01f + (0.10f / 17.0f) * (f32) (clamped_level - 1);
            percent_max_health_ratio      = max(0.01f, min(percent_max_health_ratio, 0.11f));
            f32 percent_max_health_damage = percent_max_health_ratio * target->max_health();

            // Cap percent max health damage at 300 against monsters
            if (target->is_minion()) {
                percent_max_health_damage = min(percent_max_health_damage, 300.0f);
            }

            // Total charged damage = base + AP ratio + percent max health
            return base_charged_damage + ap_ratio_charged + percent_max_health_damage;
        } else {
            // Calculate On-Hit Magic Damage part: 10-25 (based on level) + 3% AP
            f32 base_on_hit_magic_damage = 10.0f + (15.0f / 17.0f) * (f32) (clamped_level - 1);
            // Ensure it doesn't go out of bounds due to potential float inaccuracies
            base_on_hit_magic_damage = max(10.0f, min(base_on_hit_magic_damage, 25.0f));

            f32 ap_ratio_on_hit_damage    = 0.03f * ap_value;
            f32 total_on_hit_magic_damage = base_on_hit_magic_damage + ap_ratio_on_hit_damage;

            // Calculate Execute Health Threshold part: 60-150 (based on level) + 18% AP
            f32 base_execute_health_threshold = 60.0f + (90.0f / 17.0f) * (f32) (clamped_level - 1);
            base_execute_health_threshold     = max(60.0f, min(base_execute_health_threshold, 150.0f));

            f32 ap_ratio_execute_threshold     = 0.18f * ap_value;
            f32 total_execute_health_threshold = base_execute_health_threshold + ap_ratio_execute_threshold;

            // Determine final damage
            if (target->health() < total_execute_health_threshold) {
                // If target is below execute threshold, the damage effectively becomes their current health (it kills them)
                return target->health();
            } else {
                // Otherwise, it's the standard on-hit magic damage from the passive
                return player->calc_magical_damage(target, total_on_hit_magic_damage);
            }
        }
    }

    /**
     * @brief Calculates Zeri's Q (Burst Fire) damage against champions/monsters
     *
     * Q damage scales with level and total AD:
     * - Base damage: 15/17/19/21/23
     * - AD ratio: 104%/108%/112%/116%/120% total AD
     *
     * @param target The target to calculate damage against
     * @return Physical damage after armor calculation
     */
    inline static f32 calc_q_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       q_level = player->get_spell(SpellSlot::Q)->level();
        if (q_level == 0)
            return 0.0f;

        // Q base damage and AD ratio per level (1-5)
        constexpr f32 Q_BASE[]     = {15.f, 17.f, 19.f, 21.f, 23.f};
        constexpr f32 Q_AD_RATIO[] = {1.04f, 1.08f, 1.12f, 1.16f, 1.20f};

        // Clamp q_level to valid range (1-5)
        q_level   = max(1, min(5, (int) q_level));
        f32 base  = Q_BASE[q_level - 1];
        f32 ratio = Q_AD_RATIO[q_level - 1];

        // Calculate raw damage: base + (ratio * total AD)
        f32 total_ad   = player->base_physical_damage() + player->bonus_physical_damage();
        f32 raw_damage = base + ratio * total_ad;

        return target->calc_physical_damage(player, raw_damage);
    }

    inline static f32 calc_q_damage(AIMinionClient* target_minion) {
        if (!target_minion) {
            return 0.0f;  // Safety check
        }

        u32 q_level = zeri_q->level();
        if (q_level == 0) {
            return 0.0f;
        }

        // Q Damage Calculation arrays (index 0 is unused, levels 1-5 for array access)
        // Base Damage: 15 / 17 / 19 / 21 / 23
        // Total AD Ratio: 104 / 108 / 112 / 116 / 120 %
        // Ensure these values are up-to-date with the current game patch.
        f32 q_base_damage_values[] = {0.0f, 15.0f, 17.0f, 19.0f, 21.0f, 23.0f};
        // Assuming player->physical_damage() returns Total AD, as is common.
        f32 q_total_ad_ratio_values[] = {0.0f, 1.04f, 1.08f, 1.12f, 1.16f, 1.20f};

        f32 raw_q_damage = q_base_damage_values[q_level] + q_total_ad_ratio_values[q_level] * player->physical_damage();

        // Calculate actual damage on the specific minion, considering its armor
        f32 damage_on_minion = target_minion->calc_physical_damage(player, raw_q_damage);
        // log("calc_q_damage: Level %u, Raw: %.2f, OnMinion: %.2f", q_level, raw_q_damage, damage_on_minion);
        return damage_on_minion;
    }

    inline static f32 calc_w_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       w_level = player->get_spell(SpellSlot::W)->level();
        if (w_level == 0)
            return 0.0f;

        // W base damage per level (1-5)
        constexpr f32 W_BASE[]   = {30.f, 70.f, 110.f, 150.f, 190.f};
        constexpr f32 W_AD_RATIO = 1.3f;
        constexpr f32 W_AP_RATIO = 0.25f;

        // Clamp w_level to valid range (1-5) using sdk.h min/max
        w_level  = max(1, min(5, (int) w_level));
        f32 base = W_BASE[w_level - 1];

        f32 total_ad   = player->base_physical_damage() + player->bonus_physical_damage();
        f32 ap         = player->magical_damage();
        f32 raw_damage = base + W_AD_RATIO * total_ad + W_AP_RATIO * ap;

        return target->calc_physical_damage(player, raw_damage);
    }

    inline static f32 calc_r_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       r_level = player->get_spell(SpellSlot::R)->level();
        if (r_level == 0)
            return 0.0f;

        // R base damage per level (1-3)
        constexpr f32 R_BASE[]         = {175.f, 275.f, 375.f};
        constexpr f32 R_BONUS_AD_RATIO = 0.85f;
        constexpr f32 R_AP_RATIO       = 1.10f;

        // Clamp r_level to valid range (1-3) using sdk.h min/max
        r_level  = max(1, min(3, (int) r_level));
        f32 base = R_BASE[r_level - 1];

        f32 bonus_ad   = player->bonus_physical_damage();
        f32 ap         = player->magical_damage();
        f32 raw_damage = base + R_BONUS_AD_RATIO * bonus_ad + R_AP_RATIO * ap;

        return target->calc_physical_damage(player, raw_damage);
    }

    // Checks if a target is valid (not dead, targetable, visible, etc.) and optionally within a specified range.
    inline static bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX) {
        if (!target) {
            return false;
        }

        if (target->is_dead() || !target->is_targetable() || !target->is_visible() || target->is_invulnerable() || target->is_zombie()) {
            return false;
        }

        // Check if target is within range
        return get_player()->position().distance(target->position()) <= range;
    }

    // Checks if a given AIMinionClient pointer is valid and represents a standard lane/jungle minion.
    // Excludes pets, plants, wards, and traps from being considered valid minions for certain logic (e.g., farming).
    inline static bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX) {
        if (!minion) {
            return false;
        }

        if (minion->name() == SmallStr("Barrel")) {
            return false;
        }

        if (minion->is_plant() && get_world_cursor().distance(minion->position()) > 200.0f) {
            return false;
        }

        return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
    }

    // Helper function to count enemies in range
    inline static i32 enemies_in_range(f32 range) {
        i32 count = 0;
        for (auto enemy : get_enemy_heroes()) {
            if (is_valid_target(enemy, range))
                count++;
        }

        return count;
    }

    // Q range = 750 + 100% bonus attack range
    inline static f32 get_q_range() {
        const auto player = get_player();
        // Assume Q_BASE_RANGE is Zeri's base attack range
        f32 bonus_attack_range = max(0.0f, player->auto_attack_range() - Q_BASE_RANGE);
        return Q_BASE_RANGE + bonus_attack_range;
    }

    // Draws a damage indicator on enemy health bars
    inline static void draw_damage_indicator(f32 damage, AIBaseClient* enemy) {
        if (enemy->is_dead() || !enemy->is_visible() || !enemy->is_on_screen())
            return;

        // Get enemy's health bar position
        const auto bar = get_resource_bar(enemy);

        // Calculate damage percentage relative to current health, capped at 100% for overkill
        const f32 current_health = enemy->health();
        const f32 damage_percent = (current_health > 0.0f) ? min(1.0f, damage / current_health) : 0.0f;

        // Draw the damage indicator as a fill on the health bar using theme color
        const f32 bar_width    = bar.right - bar.left;
        const f32 damage_width = min(bar_width * damage_percent, bar_width);  // Clamp to bar width

        // Calculate the starting position from the right side, ensuring we don't go past the left edge
        const f32 start_x = max(bar.left, bar.right - damage_width);

        // Draw the damage indicator with the theme color, starting from the right
        renderer::draw_rect_2d_fill(
            Vec2(start_x, bar.top), Vec2(damage_width, bar.bot - bar.top), Color(THEME_RED, THEME_GREEN, THEME_BLUE, THEME_ALPHA));
    }

    inline static f32 combo_damage(AIBaseClient* target) {
        // Calculate passive damage
        const f32 passive_damage = calc_passive_damage(target, player->ammo() >= 100.0f);

        // Calculate total number of attacks
        const i32 total_attacks = get_dynamic_auto_attack_count();

        // Calculate Q damage for a single attack
        const f32 q_damage = calc_q_damage(target);
        const f32 r_damage = calc_r_damage(target);

        // Calculate total damage with Q-enhanced attacks
        // We'll use Q damage for the first attack (since Q is typically used first)
        const f32 total_damage = passive_damage + (q_damage * total_attacks) + r_damage;
        return total_damage;
    }

    inline static bool is_pos_under_turret(Position pos) {
        for (auto turret : get_enemy_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }

    inline void get_tumble_positions(Position pos, Position& qpos, bool harass = false) {
        // Find potential Tumble (Q) positions in concentric circles around the target position
        int positionsChecked    = 0;
        int maxPositionsToCheck = 75;  // Maximum number of positions to evaluate
        int circleSegmentSize   = 35;  // Size of each segment along the circle
        int currentRingIndex    = 0;   // Tracks which concentric ring we're examining

        Vec<Position> validTumblePositions = Vec<Position>();

        // Generate concentric rings of possible positions until we've checked enough
        while (positionsChecked < maxPositionsToCheck) {
            currentRingIndex++;

            // Calculate the radius of the current ring
            float currentRadius = currentRingIndex * (2 * circleSegmentSize);

            // Calculate how many points to check along this ring's circumference
            int pointsOnCircle = static_cast<int>(ceil((2 * PI * currentRadius) / (2 * static_cast<float>(circleSegmentSize))));

            // Sample points along the current ring
            for (int i = 1; i < pointsOnCircle; i++) {
                positionsChecked++;

                // Calculate angle in radians for this point on the circle
                float angleRadians = (2 * PI / (pointsOnCircle - 1)) * i;

                // Calculate the position using parametric circle equation
                Position circlePosition = Position(static_cast<float>(floor(pos.x + currentRadius * cos(angleRadians))),
                    static_cast<float>(floor(pos.y + currentRadius * sin(angleRadians))),
                    pos.height);

                // Skip positions that are beyond Vayne's Tumble range
                if (player->position().distance(circlePosition) > E_RANGE)
                    continue;

                bool enemy_in_range = false;
                for (auto enemy : get_enemy_heroes()) {
                    if (circlePosition.distance(enemy->position()) < config->min_safe_distance && !harass) {
                        enemy_in_range = true;
                        break;
                    }

                    if (circlePosition.distance(enemy->position()) < config->min_harass_safe_distance && harass) {
                        enemy_in_range = true;
                        break;
                    }
                }

                if (enemy_in_range) {
                    continue;
                }

                validTumblePositions.push(circlePosition);
            }
        }

        // Sort positions by closest to cursor for optimal tumbling
        validTumblePositions.sort(
            [](const Position& a, const Position& b) { return get_world_cursor().distance(a) > get_world_cursor().distance(b); });

        // Return the closest valid position if any were found
        if (validTumblePositions.len() > 0) {
            qpos = validTumblePositions.first();
        }
    }  // end of get_tumble_positions
};  // namespace zeri