/**
 * @file spell_manager.h
 * @brief Spell management and casting logic for <PERSON><PERSON> champion
 * 
 * This module provides organized spell casting logic for all of <PERSON><PERSON>'s abilities.
 * It handles different game modes (combo, harass, farm, flee) and manages
 * spell priorities, cooldowns, and optimal casting conditions.
 * 
 * <AUTHOR>
 * @version 2.0
 */

#pragma once
#include "sdk.h"

namespace zeri {
    namespace spells {
        
        //=============================================================================
        // SPELL CASTING RESULT TYPES
        //=============================================================================
        
        /**
         * @brief Result of a spell casting attempt
         */
        enum class CastResult {
            SUCCESS,           // Spell was cast successfully
            FAILED,           // Spell cast failed
            NOT_READY,        // Spell is on cooldown
            NO_TARGET,        // No valid target found
            OUT_OF_RANGE,     // Target is out of range
            BLOCKED,          // Cast was blocked by conditions
            INSUFFICIENT_MANA // Not enough mana
        };
        
        /**
         * @brief Spell casting context information
         */
        struct CastContext {
            ActionMode mode;           // Current action mode
            f32 current_time;         // Current game time
            f32 next_cast_time;       // Next allowed cast time
            bool has_charged_passive; // Whether player has 100 energy
            f32 mana_percent;         // Current mana percentage
        };

        //=============================================================================
        // Q SPELL MANAGEMENT
        //=============================================================================
        
        namespace q_spell {
            /**
             * @brief Attempts to cast Q in combo mode
             * 
             * Prioritizes:
             * 1. Direct target hits with high hit chance
             * 2. Anchor Q for multi-target damage when overcharged
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_combo(const CastContext& context);
            
            /**
             * @brief Attempts to cast Q in harass mode
             * 
             * Respects mana thresholds and prioritizes safe harass targets.
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_harass(const CastContext& context);
            
            /**
             * @brief Attempts to cast Q for farming
             * 
             * Handles:
             * 1. Structure targeting (Nexus > Inhibitors > Turrets)
             * 2. Last-hitting minions
             * 3. Lane pushing
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_farm(const CastContext& context);
            
            /**
             * @brief Attempts to cast Q for killstealing with anchor chains
             * 
             * Uses anchor Q mechanics to chain damage to low-health enemies
             * when Zeri is overcharged.
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_killsteal_anchor(const CastContext& context);
        }

        //=============================================================================
        // W SPELL MANAGEMENT
        //=============================================================================
        
        namespace w_spell {
            /**
             * @brief Attempts to cast W in combo mode
             * 
             * Prioritizes:
             * 1. Empowered W wall shots when available
             * 2. Direct W hits on high-priority targets
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_combo(const CastContext& context);
            
            /**
             * @brief Attempts to cast W in harass mode
             * 
             * Uses empowered W for extended range harass when safe.
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_harass(const CastContext& context);
        }

        //=============================================================================
        // E SPELL MANAGEMENT
        //=============================================================================
        
        namespace e_spell {
            /**
             * @brief Attempts to cast E in combo mode
             * 
             * Uses pathfinding to find optimal dash positions for:
             * - Better positioning
             * - Avoiding enemy abilities
             * - Maximizing damage output
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_combo(const CastContext& context);
            
            /**
             * @brief Attempts to cast E in harass mode
             * 
             * Prioritizes safe positioning while maintaining harass potential.
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_harass(const CastContext& context);
            
            /**
             * @brief Attempts to cast E for fleeing
             * 
             * Handles:
             * 1. Wall jump spots for maximum escape distance
             * 2. Fallback dash towards cursor
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_flee(const CastContext& context);
        }

        //=============================================================================
        // R SPELL MANAGEMENT
        //=============================================================================
        
        namespace r_spell {
            /**
             * @brief Attempts to cast R in combo mode
             * 
             * Casts R when:
             * 1. Multiple enemies are in range
             * 2. Can secure a kill on high-priority target
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_combo(const CastContext& context);
            
            /**
             * @brief Attempts to cast R for team fights
             * 
             * Casts R when minimum number of enemies are in range
             * for maximum team fight impact.
             * 
             * @param context Current casting context
             * @return Result of the cast attempt
             */
            CastResult cast_teamfight(const CastContext& context);
        }

        //=============================================================================
        // SPELL COORDINATION
        //=============================================================================
        
        /**
         * @brief Main spell casting coordinator
         * 
         * Manages spell priorities and casting order based on current game state.
         * Handles rate limiting and ensures optimal spell usage.
         * 
         * @param context Current casting context
         * @return true if any spell was cast, false otherwise
         */
        bool execute_spell_rotation(const CastContext& context);
        
        /**
         * @brief Creates casting context from current game state
         * 
         * @return Current casting context
         */
        CastContext create_cast_context();
        
        /**
         * @brief Checks if any spell casting is allowed
         * 
         * Considers:
         * - Player state (dead, stunned, etc.)
         * - Rate limiting
         * - Global casting conditions
         * 
         * @param context Current casting context
         * @return true if casting is allowed, false otherwise
         */
        bool can_cast_spells(const CastContext& context);
        
        /**
         * @brief Updates next cast time after successful spell cast
         * 
         * @param cast_time Time when the spell was cast
         */
        void update_cast_timing(f32 cast_time);

        //=============================================================================
        // UTILITY FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Gets spell prediction parameters for Q
         * 
         * @param is_overcharged Whether player is overcharged (affects speed/collision)
         * @return Prediction spell parameters for Q
         */
        PredSpell get_q_prediction_params(bool is_overcharged);
        
        /**
         * @brief Gets spell prediction parameters for W
         * 
         * @return Prediction spell parameters for W
         */
        PredSpell get_w_prediction_params();
        
        /**
         * @brief Checks if player should prioritize safety over aggression
         * 
         * @param context Current casting context
         * @return true if should play defensively, false otherwise
         */
        bool should_play_safe(const CastContext& context);

    } // namespace spells
} // namespace zeri
