/**
 * @file main.cpp
 * @brief Main implementation file for Zeri++ League of Legends bot
 * 
 * This file contains the core game loop and event handlers for the Zeri
 * champion automation script. It coordinates between different modules
 * and manages the overall bot behavior.
 * 
 * <AUTHOR>
 * @version 2.0
 */

#define SDK_IMPLEMENTATION
#include "main.h"

// Core modules
#include "anchor_q.h"
#include "config.h"
#include "coordinates.h"
#include "empowered_w.h"
#include "damage_calculator.h"
#include "target_selector.h"
#include "spell_manager.h"
#include "sdk.h"

namespace zeri {
    
    //=============================================================================
    // CORE UPDATE FUNCTIONS
    //=============================================================================
    
    /**
     * @brief Updates global game state variables
     */
    void update_game_state() {
        time = get_time();
        player = get_player();
    }
    
    /**
     * @brief Checks if spell casting is currently allowed
     * 
     * @return true if spells can be cast, false otherwise
     */
    bool can_cast_spells() {
        return !player->is_dead() && next_cast <= time && can_cast();
    }
    
    /**
     * @brief Main game update loop
     * 
     * Coordinates all bot functionality including spell casting,
     * movement, and decision making based on current game mode.
     */
    void on_update(void*) {
        update_game_state();
        
        if (!can_cast_spells()) {
            return;
        }
        
        // Create casting context for spell management
        auto context = spells::create_cast_context();
        
        // Execute spell rotation based on current mode
        if (spells::execute_spell_rotation(context)) {
            // If any spell was cast, update timing
            spells::update_cast_timing(time);
        }
    }

    //=============================================================================
    // DRAWING FUNCTIONS
    //=============================================================================
    
    /**
     * @brief Draws ground overlays (spell ranges, indicators, etc.)
     */
    void on_draw_ground(void*) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        // Draw W range indicator
        if (config->render_w_range) {
            const f32 pulse = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8 alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);
            renderer::draw_circle_3d(player->position(), 1200.0f, 
                Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f);
        }

        // Draw R range indicator
        if (config->render_r_range) {
            const f32 pulse = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8 alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);
            renderer::draw_circle_3d(player->position(), R_RANGE, 
                Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f);
        }

        // Draw E range indicator
        if (config->render_e_range) {
            const f32 pulse = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8 alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);
            renderer::draw_circle_3d(player->position(), E_RANGE, 
                Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f);
        }

        // Draw Q range indicator
        if (config->render_q_range) {
            const f32 pulse = (sin(get_time() * 2.0f) + 1.0f) * 0.5f;
            const u8 alpha_val = static_cast<u8>(THEME_ALPHA * 0.6f + pulse * THEME_ALPHA * 0.4f);
            auto range = targeting::get_q_range();
            renderer::draw_circle_3d(player->position(), range, 
                Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f);
        }

        // Draw jump spots
        if (config->render_jump_spots) {
            draw_jump_spots();
        }
    }
    
    /**
     * @brief Draws HUD overlays (damage indicators, etc.)
     */
    void on_draw_hud(void*) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        // Draw damage indicators on enemy health bars
        for (const auto& enemy : get_enemy_heroes()) {
            if (!enemy->is_dead() && enemy->is_visible() && enemy->is_on_screen()) {
                f32 total_damage = damage::calc_combo_damage(enemy,
                    static_cast<AIHeroClient*>(player)->level(),
                    player->ammo() >= 100.0f,
                    player->base_physical_damage() + player->bonus_physical_damage(),
                    player->bonus_physical_damage(),
                    player->magical_damage(),
                    zeri_q->level(),
                    zeri_r->level());
                draw_damage_indicator(total_damage, enemy);
            }
        }
    }

    //=============================================================================
    // UTILITY FUNCTIONS
    //=============================================================================
    
    /**
     * @brief Draws damage indicator on enemy health bar
     * 
     * @param damage Damage amount to display
     * @param target Target enemy to draw on
     */
    void draw_damage_indicator(f32 damage, AIHeroClient* target) {
        if (!target || !target->is_on_screen()) {
            return;
        }
        
        // This would need proper implementation based on the renderer API
        // For now, just a placeholder
    }

    //=============================================================================
    // EVENT HANDLERS
    //=============================================================================
    
    /**
     * @brief Handles auto attack events to manage Zeri's passive
     */
    void on_before_attack(OnBeforeAttackEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        auto passive = player->ammo();
        auto obj = const_cast<AttackableUnit*>(event->target);
        auto minion = obj->as_minion();
        auto hero = obj->as_hero();

        // Allow attacks that will kill the target with passive damage
        if (minion && damage::calc_passive_damage(minion, passive >= 100.0f, 
            static_cast<AIHeroClient*>(player)->level(), player->magical_damage()) >= minion->health() && passive < 100.0f) {
            *(event->prevent) = false;
            return;
        }

        if (hero && damage::calc_passive_damage(hero, passive >= 100.0f,
            static_cast<AIHeroClient*>(player)->level(), player->magical_damage()) >= hero->health()) {
            *(event->prevent) = false;
            return;
        }

        // Prevent attacks when passive is not charged and won't kill
        if (passive < 100.0f && minion && damage::calc_passive_damage(minion, false,
            static_cast<AIHeroClient*>(player)->level(), player->magical_damage()) < minion->health()) {
            *(event->prevent) = true;
            return;
        }

        if (passive < 100.0f && hero) {
            *(event->prevent) = true;
            return;
        }

        // Handle turret attacks
        if (obj && obj->is_turret()) {
            if (passive < 100.0f) {
                *(event->prevent) = true;
                return;
            }
        }

        *(event->prevent) = false;
    }
    
    /**
     * @brief Handles spell validation events
     */
    void on_validate_and_cast_spell(OnValidateAndCastSpellhEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        // Block manual R casts when no enemies are in range
        if (config->block_manual_r && event->manual && event->spell_data->spell_hash() == SpellHash("ZeriR")) {
            if (targeting::enemies_in_range(R_RANGE) == 0) {
                *(event->prevent) = true;
                return;
            }
        }
    }

    //=============================================================================
    // INITIALIZATION AND CLEANUP
    //=============================================================================
    
    /**
     * @brief Initializes the Zeri bot
     */
    void on_load(void*) {
        // Initialize spell instances
        player = get_player();
        zeri_q = player->get_spell(SpellSlot::Q);
        zeri_w = player->get_spell(SpellSlot::W);
        zeri_e = player->get_spell(SpellSlot::E);
        zeri_r = player->get_spell(SpellSlot::R);
        
        // Initialize configuration
        config = new Config();
    }
    
    /**
     * @brief Cleans up resources when unloading
     */
    void on_unload(void*) {
        if (config) {
            delete config;
            config = nullptr;
        }
    }

} // namespace zeri

// Plugin entry points
extern "C" {
    void on_load() {
        zeri::on_load(nullptr);
    }
    
    void on_unload() {
        zeri::on_unload(nullptr);
    }
}
