/**
 * @file damage_calculator.cpp
 * @brief Implementation of damage calculation utilities for Zeri champion
 * 
 * <AUTHOR>
 * @version 2.0
 */

#include "damage_calculator.h"
#include "main.h"

namespace zeri {
    namespace damage {
        
        //=============================================================================
        // UTILITY FUNCTIONS IMPLEMENTATION
        //=============================================================================
        
        u32 clamp_spell_level(u32 level, u32 max_level) {
            if (level == 0) level = 1; // Treat 0 as 1 for safety
            return max(1u, min(level, max_level));
        }
        
        u32 clamp_player_level(u32 level) {
            if (level == 0) level = 1; // Treat 0 as 1 for safety
            return max(1u, min(level, 18u));
        }
        
        i32 get_dynamic_auto_attack_count(u32 player_level) {
            constexpr i32 MIN_AUTO_ATTACKS = 3;
            constexpr i32 MAX_AUTO_ATTACKS = 12;
            constexpr i32 MAX_LEVEL = 18;
            
            u32 clamped_level = clamp_player_level(player_level);
            
            // Calculate scaling factor (0.0 to 1.0)
            f32 level_factor = static_cast<f32>(clamped_level - 1) / static_cast<f32>(MAX_LEVEL - 1);
            
            // Linear interpolation between MIN and MAX auto attacks
            f32 attack_count = MIN_AUTO_ATTACKS + (MAX_AUTO_ATTACKS - MIN_AUTO_ATTACKS) * level_factor;
            
            return static_cast<i32>(round(attack_count));
        }

        //=============================================================================
        // PASSIVE DAMAGE IMPLEMENTATION
        //=============================================================================
        
        f32 calc_passive_damage(AIBaseClient* target, bool charged, u32 player_level, f32 ap_value) {
            if (!target) {
                return 0.0f;
            }
            
            u32 clamped_level = clamp_player_level(player_level);
            
            if (charged) {
                // Charged passive: 75-160 (+110% AP) + 1%-11% max health magic damage
                f32 base_charged_damage = 75.0f + (85.0f / 17.0f) * static_cast<f32>(clamped_level - 1);
                base_charged_damage = max(75.0f, min(base_charged_damage, 160.0f));
                
                f32 ap_ratio_charged = 1.10f * ap_value;
                
                // Calculate percent max health damage (1%-11% based on level)
                f32 percent_max_health_ratio = 0.01f + (0.10f / 17.0f) * static_cast<f32>(clamped_level - 1);
                percent_max_health_ratio = max(0.01f, min(percent_max_health_ratio, 0.11f));
                f32 percent_max_health_damage = percent_max_health_ratio * target->max_health();
                
                // Cap percent max health damage at 300 against monsters
                if (target->is_minion()) {
                    percent_max_health_damage = min(percent_max_health_damage, 300.0f);
                }
                
                // Total charged damage = base + AP ratio + percent max health
                f32 total_raw_damage = base_charged_damage + ap_ratio_charged + percent_max_health_damage;
                return target->calc_magical_damage(player, total_raw_damage);
            } else {
                // Normal passive: On-Hit Magic Damage: 10-25 (+3% AP)
                // Execute threshold: 60-150 (+18% AP)
                f32 base_on_hit_magic_damage = 10.0f + (15.0f / 17.0f) * static_cast<f32>(clamped_level - 1);
                base_on_hit_magic_damage = max(10.0f, min(base_on_hit_magic_damage, 25.0f));
                
                f32 ap_ratio_on_hit_damage = 0.03f * ap_value;
                f32 total_on_hit_magic_damage = base_on_hit_magic_damage + ap_ratio_on_hit_damage;
                
                // Calculate Execute Health Threshold: 60-150 (+18% AP)
                f32 base_execute_health_threshold = 60.0f + (90.0f / 17.0f) * static_cast<f32>(clamped_level - 1);
                base_execute_health_threshold = max(60.0f, min(base_execute_health_threshold, 150.0f));
                
                f32 ap_ratio_execute_threshold = 0.18f * ap_value;
                f32 total_execute_health_threshold = base_execute_health_threshold + ap_ratio_execute_threshold;
                
                // Determine final damage
                if (target->health() < total_execute_health_threshold) {
                    // If target is below execute threshold, return their current health (kills them)
                    return target->health();
                } else {
                    // Otherwise, return the standard on-hit magic damage
                    return target->calc_magical_damage(player, total_on_hit_magic_damage);
                }
            }
        }

        //=============================================================================
        // Q ABILITY DAMAGE IMPLEMENTATION
        //=============================================================================
        
        f32 calc_q_damage(AIBaseClient* target, u32 q_level, f32 total_ad) {
            if (!target || q_level == 0) {
                return 0.0f;
            }
            
            u32 clamped_level = clamp_spell_level(q_level, 5);
            f32 base = Q_BASE_DAMAGE[clamped_level - 1];
            f32 ratio = Q_AD_RATIO[clamped_level - 1];
            
            f32 raw_damage = base + ratio * total_ad;
            return target->calc_physical_damage(player, raw_damage);
        }
        
        f32 calc_q_damage(AIMinionClient* target_minion, u32 q_level, f32 total_ad) {
            if (!target_minion || q_level == 0) {
                return 0.0f;
            }
            
            u32 clamped_level = clamp_spell_level(q_level, 5);
            f32 base = Q_BASE_DAMAGE[clamped_level - 1];
            f32 ratio = Q_AD_RATIO[clamped_level - 1];
            
            f32 raw_damage = base + ratio * total_ad;
            return target_minion->calc_physical_damage(player, raw_damage);
        }

        //=============================================================================
        // W ABILITY DAMAGE IMPLEMENTATION
        //=============================================================================
        
        f32 calc_w_damage(AIBaseClient* target, u32 w_level, f32 total_ad, f32 ap_value) {
            if (!target || w_level == 0) {
                return 0.0f;
            }
            
            u32 clamped_level = clamp_spell_level(w_level, 5);
            f32 base = W_BASE_DAMAGE[clamped_level - 1];
            
            f32 raw_damage = base + W_AD_RATIO * total_ad + W_AP_RATIO * ap_value;
            return target->calc_physical_damage(player, raw_damage);
        }

        //=============================================================================
        // R ABILITY DAMAGE IMPLEMENTATION
        //=============================================================================
        
        f32 calc_r_damage(AIBaseClient* target, u32 r_level, f32 bonus_ad, f32 ap_value) {
            if (!target || r_level == 0) {
                return 0.0f;
            }
            
            u32 clamped_level = clamp_spell_level(r_level, 3);
            f32 base = R_BASE_DAMAGE[clamped_level - 1];
            
            f32 raw_damage = base + R_BONUS_AD_RATIO * bonus_ad + R_AP_RATIO * ap_value;
            return target->calc_physical_damage(player, raw_damage);
        }

        //=============================================================================
        // COMBO DAMAGE IMPLEMENTATION
        //=============================================================================
        
        f32 calc_combo_damage(AIBaseClient* target, u32 player_level, bool has_charged_passive,
                             f32 total_ad, f32 bonus_ad, f32 ap_value, u32 q_level, u32 r_level) {
            if (!target) {
                return 0.0f;
            }
            
            // Calculate passive damage
            f32 passive_damage = calc_passive_damage(target, has_charged_passive, player_level, ap_value);
            
            // Calculate number of attacks based on level
            i32 total_attacks = get_dynamic_auto_attack_count(player_level);
            
            // Calculate Q damage for multiple attacks
            f32 q_damage = calc_q_damage(target, q_level, total_ad);
            f32 total_q_damage = q_damage * total_attacks;
            
            // Calculate R damage if available
            f32 r_damage = calc_r_damage(target, r_level, bonus_ad, ap_value);
            
            return passive_damage + total_q_damage + r_damage;
        }

    } // namespace damage
} // namespace zeri
