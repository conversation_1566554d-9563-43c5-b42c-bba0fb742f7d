/**
 * @file target_selector.h
 * @brief Target selection and validation utilities for Zeri champion
 * 
 * This module provides comprehensive target selection logic including
 * target validation, range checking, priority calculation, and special
 * target filtering for different game modes and situations.
 * 
 * <AUTHOR>
 * @version 2.0
 */

#pragma once
#include "sdk.h"

namespace zeri {
    namespace targeting {
        
        //=============================================================================
        // TARGET VALIDATION FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Checks if a target is valid for attacking
         * 
         * Validates that the target is:
         * - Not null
         * - Not dead
         * - Targetable
         * - Visible
         * - Not invulnerable
         * - Not a zombie
         * - Within specified range (optional)
         * 
         * @param target The target to validate
         * @param range Maximum range to target (FLT_MAX for no range check)
         * @return true if target is valid, false otherwise
         */
        bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX);
        
        /**
         * @brief Checks if a minion is valid for farming/targeting
         * 
         * Validates minion and excludes:
         * - Pets
         * - Plants (unless cursor is nearby)
         * - Wards
         * - Traps
         * - Allied minions
         * - Special objects like Gangplank barrels
         * 
         * @param minion The minion to validate
         * @param range Maximum range to minion (FLT_MAX for no range check)
         * @return true if minion is valid for targeting, false otherwise
         */
        bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX);
        
        /**
         * @brief Checks if a position is under enemy turret range
         * 
         * @param pos Position to check
         * @param turret_range Turret attack range (default 1000)
         * @return true if position is under enemy turret, false otherwise
         */
        bool is_pos_under_turret(Position pos, f32 turret_range = 1000.0f);

        //=============================================================================
        // TARGET COUNTING FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Counts enemy champions within specified range
         * 
         * @param range Range to check for enemies
         * @param from_position Position to check from (default: player position)
         * @return Number of valid enemy champions in range
         */
        i32 enemies_in_range(f32 range, Position from_position = Position());
        
        /**
         * @brief Counts valid minions within specified range
         * 
         * @param range Range to check for minions
         * @param from_position Position to check from (default: player position)
         * @param include_jungle Whether to include jungle monsters
         * @return Number of valid minions in range
         */
        i32 minions_in_range(f32 range, Position from_position = Position(), bool include_jungle = true);

        //=============================================================================
        // TARGET PRIORITY FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Target priority levels for different situations
         */
        enum class TargetPriority {
            LOWEST = 0,
            LOW = 1,
            MEDIUM = 2,
            HIGH = 3,
            HIGHEST = 4
        };
        
        /**
         * @brief Calculates target priority based on various factors
         * 
         * Considers:
         * - Target health percentage
         * - Distance to player
         * - Target type (ADC, Support, etc.)
         * - Threat level
         * - Killability
         * 
         * @param target The target to evaluate
         * @param consider_damage Whether to factor in damage calculations
         * @return Priority level for the target
         */
        TargetPriority calculate_target_priority(AIHeroClient* target, bool consider_damage = true);
        
        /**
         * @brief Gets the best target for combo mode
         * 
         * Prioritizes targets based on:
         * - Killability with current combo
         * - Distance and accessibility
         * - Target importance (ADC > Mid > Support > Tank)
         * 
         * @param max_range Maximum range to consider targets
         * @return Best target for combo, or nullptr if none found
         */
        AIHeroClient* get_best_combo_target(f32 max_range);
        
        /**
         * @brief Gets the best target for harass mode
         * 
         * Prioritizes safe harass targets:
         * - Targets within safe range
         * - High value targets (ADC, Mid)
         * - Targets with low escape potential
         * 
         * @param max_range Maximum range to consider targets
         * @param safe_distance Minimum safe distance from other enemies
         * @return Best target for harass, or nullptr if none found
         */
        AIHeroClient* get_best_harass_target(f32 max_range, f32 safe_distance = 600.0f);

        //=============================================================================
        // FARMING TARGET FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Farming target types
         */
        enum class FarmTargetType {
            LAST_HIT,      // Only last-hittable minions
            PUSH,          // Any minion for pushing
            STRUCTURES,    // Turrets, inhibitors, nexus
            JUNGLE         // Jungle monsters
        };
        
        /**
         * @brief Gets the best farming target based on mode
         * 
         * @param target_type Type of farming target to find
         * @param max_range Maximum range to consider targets
         * @param damage_threshold Minimum damage required for last-hitting
         * @return Best farming target, or nullptr if none found
         */
        AIBaseClient* get_best_farm_target(FarmTargetType target_type, f32 max_range, f32 damage_threshold = 0.0f);
        
        /**
         * @brief Finds minions that can be last-hit with Q
         * 
         * Predicts minion health at impact time and finds those
         * that will be killable with Q damage.
         * 
         * @param q_damage Q damage against minions
         * @param cast_delay Q cast delay
         * @param projectile_speed Q projectile speed
         * @param max_range Maximum Q range
         * @return Vector of last-hittable minions
         */
        Vec<AIMinionClient*> get_last_hittable_minions(f32 q_damage, f32 cast_delay, f32 projectile_speed, f32 max_range);

        //=============================================================================
        // UTILITY FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Gets current Q range including bonus attack range
         * 
         * Zeri's Q range = 750 + 100% bonus attack range
         * 
         * @return Current effective Q range
         */
        f32 get_q_range();
        
        /**
         * @brief Checks if spell is ready considering latency
         * 
         * @param spell The spell to check
         * @param extra_buffer Additional time buffer (default 0.5s)
         * @return true if spell is ready to cast, false otherwise
         */
        bool is_spell_ready(SpellInstance* spell, f32 extra_buffer = 0.5f);
        
        /**
         * @brief Gets the closest valid target to a position
         * 
         * @param targets Vector of potential targets
         * @param position Position to check distance from
         * @param max_range Maximum range to consider
         * @return Closest valid target, or nullptr if none found
         */
        template<typename T>
        T* get_closest_target(const Vec<T*>& targets, Position position, f32 max_range = FLT_MAX);

    } // namespace targeting
} // namespace zeri
