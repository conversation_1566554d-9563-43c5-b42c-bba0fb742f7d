/**
 * @file damage_calculator.h
 * @brief Damage calculation utilities for Zeri champion
 * 
 * This module provides comprehensive damage calculation functions for all of
 * <PERSON><PERSON>'s abilities including passive, Q, W, E, and R. It handles different
 * target types and takes into account resistances, scaling, and special effects.
 * 
 * <AUTHOR>
 * @version 2.0
 */

#pragma once
#include "sdk.h"

namespace zeri {
    namespace damage {
        //=============================================================================
        // DAMAGE CALCULATION CONSTANTS
        //=============================================================================
        
        /** @brief Zeri Q base damage values per level (1-5) */
        constexpr f32 Q_BASE_DAMAGE[] = {15.0f, 17.0f, 19.0f, 21.0f, 23.0f};
        
        /** @brief Zeri Q total AD ratio per level (1-5) */
        constexpr f32 Q_AD_RATIO[] = {1.04f, 1.08f, 1.12f, 1.16f, 1.20f};
        
        /** @brief Zeri W base damage values per level (1-5) */
        constexpr f32 W_BASE_DAMAGE[] = {30.0f, 70.0f, 110.0f, 150.0f, 190.0f};
        
        /** @brief Zeri W total AD ratio */
        constexpr f32 W_AD_RATIO = 1.3f;
        
        /** @brief Zeri W AP ratio */
        constexpr f32 W_AP_RATIO = 0.25f;
        
        /** @brief Zeri R base damage values per level (1-3) */
        constexpr f32 R_BASE_DAMAGE[] = {175.0f, 275.0f, 375.0f};
        
        /** @brief Zeri R bonus AD ratio */
        constexpr f32 R_BONUS_AD_RATIO = 0.85f;
        
        /** @brief Zeri R AP ratio */
        constexpr f32 R_AP_RATIO = 1.10f;

        //=============================================================================
        // PASSIVE DAMAGE CALCULATION
        //=============================================================================
        
        /**
         * @brief Calculates Zeri's passive (Burst Fire) damage
         * 
         * Zeri's passive has two modes:
         * - Normal: 10-25 (+3% AP) magic damage with execute threshold 60-150 (+18% AP)
         * - Charged (100 energy): 75-160 (+110% AP) + 1%-11% max health magic damage
         * 
         * @param target The target to calculate damage against
         * @param charged Whether Zeri has 100 energy (charged state)
         * @param player_level Current player level (1-18)
         * @param ap_value Player's ability power
         * @return Actual damage after magic resistance calculation
         */
        f32 calc_passive_damage(AIBaseClient* target, bool charged, u32 player_level, f32 ap_value);

        //=============================================================================
        // Q ABILITY DAMAGE CALCULATION
        //=============================================================================
        
        /**
         * @brief Calculates Zeri's Q (Burst Fire) damage against champions/monsters
         * 
         * Q damage scales with level and total AD:
         * - Base damage: 15/17/19/21/23
         * - AD ratio: 104%/108%/112%/116%/120% total AD
         * 
         * @param target The target to calculate damage against
         * @param q_level Q spell level (1-5)
         * @param total_ad Player's total attack damage
         * @return Physical damage after armor calculation
         */
        f32 calc_q_damage(AIBaseClient* target, u32 q_level, f32 total_ad);
        
        /**
         * @brief Calculates Zeri's Q damage specifically against minions
         * 
         * Overloaded version for minion-specific calculations that may have
         * different damage values or special interactions.
         * 
         * @param target_minion The minion target
         * @param q_level Q spell level (1-5)
         * @param total_ad Player's total attack damage
         * @return Physical damage after armor calculation
         */
        f32 calc_q_damage(AIMinionClient* target_minion, u32 q_level, f32 total_ad);

        //=============================================================================
        // W ABILITY DAMAGE CALCULATION
        //=============================================================================
        
        /**
         * @brief Calculates Zeri's W (Ultrashock Laser) damage
         * 
         * W damage scales with level, total AD, and AP:
         * - Base damage: 30/70/110/150/190
         * - AD ratio: 130% total AD
         * - AP ratio: 25% AP
         * 
         * @param target The target to calculate damage against
         * @param w_level W spell level (1-5)
         * @param total_ad Player's total attack damage
         * @param ap_value Player's ability power
         * @return Physical damage after armor calculation
         */
        f32 calc_w_damage(AIBaseClient* target, u32 w_level, f32 total_ad, f32 ap_value);

        //=============================================================================
        // R ABILITY DAMAGE CALCULATION
        //=============================================================================
        
        /**
         * @brief Calculates Zeri's R (Lightning Crash) damage
         * 
         * R damage scales with level, bonus AD, and AP:
         * - Base damage: 175/275/375
         * - Bonus AD ratio: 85% bonus AD
         * - AP ratio: 110% AP
         * 
         * @param target The target to calculate damage against
         * @param r_level R spell level (1-3)
         * @param bonus_ad Player's bonus attack damage
         * @param ap_value Player's ability power
         * @return Physical damage after armor calculation
         */
        f32 calc_r_damage(AIBaseClient* target, u32 r_level, f32 bonus_ad, f32 ap_value);

        //=============================================================================
        // COMBO DAMAGE CALCULATION
        //=============================================================================
        
        /**
         * @brief Calculates total combo damage against a target
         * 
         * Estimates realistic damage output considering:
         * - Passive damage (charged if 100 energy)
         * - Multiple Q casts based on level
         * - R damage if available
         * 
         * @param target The target to calculate damage against
         * @param player_level Current player level
         * @param has_charged_passive Whether player has 100 energy
         * @param total_ad Player's total attack damage
         * @param bonus_ad Player's bonus attack damage
         * @param ap_value Player's ability power
         * @param q_level Q spell level
         * @param r_level R spell level
         * @return Total estimated combo damage
         */
        f32 calc_combo_damage(AIBaseClient* target, u32 player_level, bool has_charged_passive,
                             f32 total_ad, f32 bonus_ad, f32 ap_value, u32 q_level, u32 r_level);

        //=============================================================================
        // UTILITY FUNCTIONS
        //=============================================================================
        
        /**
         * @brief Calculates dynamic auto attack count based on player level
         * 
         * Used for realistic combo damage estimation. Scales from 3 attacks
         * at level 1 to 12 attacks at level 18.
         * 
         * @param player_level Current player level (1-18)
         * @return Number of auto attacks to consider in damage calculations
         */
        i32 get_dynamic_auto_attack_count(u32 player_level);
        
        /**
         * @brief Safely clamps spell level to valid range
         * 
         * @param level Spell level to clamp
         * @param max_level Maximum valid level for the spell
         * @return Clamped level (1 to max_level)
         */
        u32 clamp_spell_level(u32 level, u32 max_level);
        
        /**
         * @brief Safely clamps player level to valid range
         * 
         * @param level Player level to clamp
         * @return Clamped level (1 to 18)
         */
        u32 clamp_player_level(u32 level);

    } // namespace damage
} // namespace zeri
