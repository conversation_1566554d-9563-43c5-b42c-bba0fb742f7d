/**
 * @file target_selector.cpp
 * @brief Implementation of target selection and validation utilities
 * 
 * <AUTHOR>
 * @version 2.0
 */

#include "target_selector.h"
#include "main.h"
#include "damage_calculator.h"

namespace zeri {
    namespace targeting {
        
        //=============================================================================
        // TARGET VALIDATION IMPLEMENTATION
        //=============================================================================
        
        bool is_valid_target(AIBaseClient* target, f32 range) {
            if (!target) {
                return false;
            }
            
            if (target->is_dead() || !target->is_targetable() || !target->is_visible() || 
                target->is_invulnerable() || target->is_zombie()) {
                return false;
            }
            
            // Check if target is within range
            if (range != FLT_MAX) {
                return get_player()->position().distance(target->position()) <= range;
            }
            
            return true;
        }
        
        bool is_valid_minion(AIMinionClient* minion, f32 range) {
            if (!minion) {
                return false;
            }
            
            // Exclude Gangplank barrels
            if (minion->name() == SmallStr("Barrel")) {
                return false;
            }
            
            // Exclude plants unless cursor is nearby
            if (minion->is_plant() && get_world_cursor().distance(minion->position()) > 200.0f) {
                return false;
            }
            
            // Must be enemy, not a trap, and pass basic validation
            return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
        }
        
        bool is_pos_under_turret(Position pos, f32 turret_range) {
            for (auto turret : get_enemy_turrets()) {
                if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen()) {
                    continue;
                }
                
                if (turret->position().distance(pos) < turret_range) {
                    return true;
                }
            }
            
            return false;
        }

        //=============================================================================
        // TARGET COUNTING IMPLEMENTATION
        //=============================================================================
        
        i32 enemies_in_range(f32 range, Position from_position) {
            Position check_pos = from_position.is_valid() ? from_position : get_player()->position();
            i32 count = 0;
            
            for (auto enemy : get_enemy_heroes()) {
                if (is_valid_target(enemy) && check_pos.distance(enemy->position()) <= range) {
                    count++;
                }
            }
            
            return count;
        }
        
        i32 minions_in_range(f32 range, Position from_position, bool include_jungle) {
            Position check_pos = from_position.is_valid() ? from_position : get_player()->position();
            i32 count = 0;
            
            for (auto minion : get_minions()) {
                if (!is_valid_minion(minion, range)) {
                    continue;
                }
                
                if (!include_jungle && minion->is_jungle_monster()) {
                    continue;
                }
                
                if (check_pos.distance(minion->position()) <= range) {
                    count++;
                }
            }
            
            return count;
        }

        //=============================================================================
        // TARGET PRIORITY IMPLEMENTATION
        //=============================================================================
        
        TargetPriority calculate_target_priority(AIHeroClient* target, bool consider_damage) {
            if (!target || !is_valid_target(target)) {
                return TargetPriority::LOWEST;
            }
            
            f32 priority_score = 0.0f;
            
            // Health percentage factor (lower health = higher priority)
            f32 health_percent = target->health() / target->max_health();
            priority_score += (1.0f - health_percent) * 2.0f; // 0-2 points
            
            // Distance factor (closer = higher priority)
            f32 distance = get_player()->position().distance(target->position());
            f32 distance_factor = max(0.0f, 1.0f - (distance / 1200.0f)); // Normalize to 1200 range
            priority_score += distance_factor * 1.0f; // 0-1 points
            
            // Role-based priority (ADC > Mid > Support > Tank)
            // This is a simplified heuristic based on champion characteristics
            if (target->base_physical_damage() > target->magical_damage()) {
                priority_score += 1.5f; // Likely ADC
            } else {
                priority_score += 1.0f; // Likely AP carry
            }
            
            // Damage consideration
            if (consider_damage) {
                f32 combo_damage = damage::calc_combo_damage(target, 
                    static_cast<AIHeroClient*>(player)->level(),
                    player->ammo() >= 100.0f,
                    player->base_physical_damage() + player->bonus_physical_damage(),
                    player->bonus_physical_damage(),
                    player->magical_damage(),
                    zeri_q->level(),
                    zeri_r->level());
                
                if (combo_damage >= target->health()) {
                    priority_score += 3.0f; // Killable target gets highest priority
                }
            }
            
            // Convert score to priority enum
            if (priority_score >= 5.0f) return TargetPriority::HIGHEST;
            if (priority_score >= 4.0f) return TargetPriority::HIGH;
            if (priority_score >= 2.5f) return TargetPriority::MEDIUM;
            if (priority_score >= 1.0f) return TargetPriority::LOW;
            return TargetPriority::LOWEST;
        }
        
        AIHeroClient* get_best_combo_target(f32 max_range) {
            AIHeroClient* best_target = nullptr;
            TargetPriority best_priority = TargetPriority::LOWEST;
            
            for (auto enemy : get_enemy_heroes()) {
                if (!is_valid_target(enemy, max_range)) {
                    continue;
                }
                
                TargetPriority priority = calculate_target_priority(enemy, true);
                if (priority > best_priority) {
                    best_priority = priority;
                    best_target = enemy;
                }
            }
            
            return best_target;
        }
        
        AIHeroClient* get_best_harass_target(f32 max_range, f32 safe_distance) {
            AIHeroClient* best_target = nullptr;
            TargetPriority best_priority = TargetPriority::LOWEST;
            
            for (auto enemy : get_enemy_heroes()) {
                if (!is_valid_target(enemy, max_range)) {
                    continue;
                }
                
                // Check if it's safe to harass (no other enemies too close)
                i32 nearby_enemies = enemies_in_range(safe_distance, enemy->position()) - 1; // Exclude the target itself
                if (nearby_enemies > 1) {
                    continue; // Too dangerous
                }
                
                TargetPriority priority = calculate_target_priority(enemy, false);
                if (priority > best_priority) {
                    best_priority = priority;
                    best_target = enemy;
                }
            }
            
            return best_target;
        }

        //=============================================================================
        // FARMING TARGET IMPLEMENTATION
        //=============================================================================
        
        AIBaseClient* get_best_farm_target(FarmTargetType target_type, f32 max_range, f32 damage_threshold) {
            switch (target_type) {
                case FarmTargetType::STRUCTURES: {
                    // Priority: Nexus > Inhibitors > Turrets
                    auto nexus = get_enemy_nexus();
                    if (nexus && is_valid_target(nexus, max_range)) {
                        return nexus;
                    }
                    
                    for (auto inhibitor : get_enemy_inhibitors()) {
                        if (is_valid_target(inhibitor, max_range)) {
                            return inhibitor;
                        }
                    }
                    
                    for (auto turret : get_enemy_turrets()) {
                        if (is_valid_target(turret, max_range)) {
                            return turret;
                        }
                    }
                    break;
                }
                
                case FarmTargetType::LAST_HIT: {
                    AIMinionClient* best_minion = nullptr;
                    f32 lowest_health = FLT_MAX;
                    
                    for (auto minion : get_minions()) {
                        if (!is_valid_minion(minion, max_range)) {
                            continue;
                        }
                        
                        if (damage_threshold > 0.0f && minion->health() > damage_threshold) {
                            continue; // Can't last hit this minion
                        }
                        
                        if (minion->health() < lowest_health) {
                            lowest_health = minion->health();
                            best_minion = minion;
                        }
                    }
                    
                    return best_minion;
                }
                
                case FarmTargetType::PUSH: {
                    // Find closest minion for pushing
                    AIMinionClient* closest_minion = nullptr;
                    f32 closest_distance = FLT_MAX;
                    
                    for (auto minion : get_minions()) {
                        if (!is_valid_minion(minion, max_range)) {
                            continue;
                        }
                        
                        f32 distance = get_player()->position().distance(minion->position());
                        if (distance < closest_distance) {
                            closest_distance = distance;
                            closest_minion = minion;
                        }
                    }
                    
                    return closest_minion;
                }
                
                case FarmTargetType::JUNGLE: {
                    for (auto minion : get_minions()) {
                        if (is_valid_minion(minion, max_range) && minion->is_jungle_monster()) {
                            return minion;
                        }
                    }
                    break;
                }
            }
            
            return nullptr;
        }

        //=============================================================================
        // UTILITY IMPLEMENTATION
        //=============================================================================
        
        f32 get_q_range() {
            const auto player_obj = get_player();
            f32 bonus_attack_range = max(0.0f, player_obj->auto_attack_range() - Q_BASE_RANGE);
            return Q_BASE_RANGE + bonus_attack_range;
        }
        
        bool is_spell_ready(SpellInstance* spell, f32 extra_buffer) {
            if (!spell) {
                return false;
            }
            
            return spell->cool_down_remaining() <= (get_latency() / 1000.0f) + extra_buffer;
        }

    } // namespace targeting
} // namespace zeri
